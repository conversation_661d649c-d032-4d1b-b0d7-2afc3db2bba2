import {
    Entity,
    EntityClass,
    EntityAsSimpleObject,
    IntegerField,
    StringField
} from "@groupk/horizon2-core";

/**
 * ObjectNames entity representing object names in the Baserow database
 * Table ID: 1733
 *
 * Field mappings from Baserow API documentation:
 * - field_16710: code (string)
 * - field_16709: name (string)
 * - field_16711: brand (integer or string, single select with options)
 *   Options: 7485=Sunmi, 7486=Jonkuu, 7487=Quilive, 7488=PAX
 */

// Brand options for the brand field
export const BRAND_OPTIONS = {
    SUNMI: 7485,
    JONKUU: 7486,
    QUILIVE: 7487,
    PAX: 7488
} as const;

export const BRAND_LABELS = {
    [BRAND_OPTIONS.SUNMI]: 'Sunmi',
    [BRAND_OPTIONS.JONKUU]: 'Jon<PERSON><PERSON>',
    [BRAND_OPTIONS.QUILIVE]: 'Quilive',
    [BRAND_OPTIONS.PAX]: 'PAX'
} as const;

@EntityClass()
export class ObjectNamesEntity extends Entity {
    @IntegerField() id: number;

    /** field_16710: Object code */
    @StringField() code: string;

    /** field_16709: Object name */
    @StringField() name: string;

    /** field_16711: Brand selection (integer or string) */
    @IntegerField() brand: number;

    constructor({
        id,
        code,
        name,
        brand
    }: EntityAsSimpleObject<ObjectNamesEntity>) {
        super();
        this.id = id;
        this.code = code;
        this.name = name;
        this.brand = brand;
    }
}
