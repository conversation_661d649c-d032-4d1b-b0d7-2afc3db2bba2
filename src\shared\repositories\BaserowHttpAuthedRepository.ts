import {
    AbstractHttpDescriptorRepository, AbstractHttpDescriptorRepositoryOptions,
    DefaultAbstractHttpDescriptorRepositoryOptions,
    HttpHeaderUtils,
    HttpRouteDescriptorAggregate
} from '@groupk/horizon2-core';
import {BASEROW_CONFIG} from "../baserowContracts";

export abstract class BaserowHttpAuthedRepository<ContractAggregate extends HttpRouteDescriptorAggregate> extends AbstractHttpDescriptorRepository<ContractAggregate>{
    constructor(contractAggregate: ContractAggregate, options : Partial<AbstractHttpDescriptorRepositoryOptions> = DefaultAbstractHttpDescriptorRepositoryOptions) {
        super(BASEROW_CONFIG.BASE_URL, contractAggregate, options);

        this.hookOn('beforeRequest',  (request) => {
            request.headers = request.headers ?? {};
            request.headers[HttpHeaderUtils.Authorization] = 'Token fCK4Uh9UE8bo8KKRM8dHPvReYiQuf7wL';
            return request;
        })
    }

}