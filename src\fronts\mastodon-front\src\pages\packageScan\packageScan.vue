<script lang="ts" src="./packageScan.ts"/>

<style lang="sass" scoped>
@import './packageScan.scss'
</style>

<template>
    <div id="package-scan-page">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>
        <div class="container" v-else>
            <div class="actions">
                <div class="action-group" @click="showForm = true;">
                    <div class="action">
                        <i class="fa-regular fa-plus"></i>
                    </div>
                    <span class="text"> Nouveau colis </span>
                </div>

                <div class="action-group" @click="loadPackages()">
                    <div class="action">
                        <i class="fa-regular fa-arrows-rotate"></i>
                    </div>
                    <span class="text"> Recharger </span>
                </div>
            </div>

            <h3> Colis ({{ existingPackages.length }}) </h3>

            <div class="packages">
                <div class="package" :class="{done: packageData.validated}" v-for="packageData of existingPackages" @click="selectedPackage = packageData">
                    <div class="top">
                        <span class="name"> {{ packageData.name }} </span>

                        <i class="fa-regular fa-check" v-if="packageData.validated"></i>
                    </div>
                    <div class="bottom">
                        <span class="subtitle" v-if="!packageData.shipping && !packageData.return">
                            Pas de code de suivi
                        </span>

                        <span class="subtitle" v-if="packageData.shipping">
                           <i class="fa-regular fa-arrow-up"></i>
                            {{ packageData.shipping }}
                        </span>

                        <div class="separator" v-if="packageData.shipping && packageData.return"></div>

                        <span class="subtitle" v-if="packageData.return">
                            <i class="fa-regular fa-arrow-down"></i>
                            {{ packageData.return }}
                        </span>

                    </div>
                </div>
            </div>
        </div>

        <package
            v-if="selectedPackage"
            :package-data="selectedPackage"
            :object-translations="objectTranslations"
            @updated-package="updatedPackage($event)"
            @close="selectedPackage = null; setupListener()"
        ></package>

        <package-form
            v-if="showForm"
            @created="createdPackage($event)"
            @close="showForm = false; setupListener()"
        ></package-form>
    </div>
</template>