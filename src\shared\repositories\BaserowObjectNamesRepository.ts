import {BaserowHttpAuthedRepository} from "./BaserowHttpAuthedRepository";
import {BaserowPackageContractAggregate} from "../baserowContracts/Package/BaserowPackageContract";
import {BaserowObjectNamesContractAggregate} from "../baserowContracts/ObjectNames/BaserowObjectNamesContract";

export class BaserowObjectNamesRepository extends BaserowHttpAuthedRepository<typeof BaserowObjectNamesContractAggregate>{
    constructor() {
        super(BaserowObjectNamesContractAggregate);
    }
}