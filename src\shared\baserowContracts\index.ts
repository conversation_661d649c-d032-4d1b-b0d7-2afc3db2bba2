/**
 * Main export file for Baserow contracts and related classes
 *
 * This file exports all the contracts and API classes
 * needed to interact with the Baserow API for the three tables:
 * - Package (Table ID: 1731)
 * - Objects (Table ID: 1732)
 * - ObjectNames (Table ID: 1733)
 */

/**
 * Baserow configuration constants
 */
export const BASEROW_CONFIG = Object.freeze({
    BASE_URL: 'https://baserow-api.lab.weecop.fr',
    DATABASE_ID: 446,
    TABLES: Object.freeze({
        PACKAGE: 1731,
        OBJECTS: 1732,
        OBJECT_NAMES: 1733
    })
} as const);


