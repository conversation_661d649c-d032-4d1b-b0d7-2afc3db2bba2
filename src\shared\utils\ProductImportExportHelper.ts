import {ColumnBindingError, EntityBuilderReturn, ImportColumn, ParsedCsv} from "@groupk/vue3-interface-sdk";
import {
	AutoWired,
	CountryEnum,
	Entity, EntityAsSimpleObject, EntityClass, EntityField, GetInstance, StringField,
	TypedQuerySearch,
	Uuid,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {
	BillingAccountCodeApiOut,
	BillingRegionApiOut, Constants,
	ProductApiOut, ProductHttpProductContractSearchConfig,
	ProductModel,
	ProductPriceApi,
	ProductRevisionApiIn,
	ProductType,
	UuidScopeProductBillingRegion,
	uuidScopeProductProduct,
	UuidScopeProductProduct
} from "@groupk/mastodon-core";
import {MoneyFilter} from "../filters/Money";
import {ProductRepository} from "../repositories/ProductRepository";
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import ProductUtils from "./ProductUtils";
import {ColumnBindingsError} from "@groupk/vue3-interface-sdk";
import {
	ProductGroupData,
	ProductGroupItemData,
	ProductRevisionPriceForItemData
} from "../mastodonCoreFront/product/ProductRevisionData";
import {ExportFileType, MultiFormatExporter} from "./MultiFormatExporter";
import {BillingAccountRepository} from "../repositories/BillingAccountRepository";
import {
	BillingAccountCodeCreationApiIn
} from "@groupk/mastodon-core";
import {SpecificState} from "../../fronts/product-front/src/SpecificState";

@EntityClass()
export class ProductRevisionImportApi extends Entity {
	@EntityField(ProductRevisionApiIn) revisionApiIn: ProductRevisionApiIn;
	@StringField({nullable: true}) billingAccountCode: string|null;

	constructor({revisionApiIn, billingAccountCode}: EntityAsSimpleObject<ProductRevisionImportApi>) {
		super();
		this.revisionApiIn = revisionApiIn;
		this.billingAccountCode = billingAccountCode;
	}
}

export class ProductImportExportHelper {
	private establishmentUid: VisualScopedUuid<UuidScopeEstablishment>;
	private billingRegions: BillingRegionApiOut[];
	public static csvImportColumns: ImportColumn[] = [
		{
			name: 'uid',
			translation: 'Identifiant unique',
			primaryKey: true,
			matching: ['uid'],
			required: false
		}, {
			name: 'name',
			translation: 'Nom',
			matching: ['name','Name', 'nom', 'Nom'],
			required: true
		}, {
			name: 'priceWithoutTaxes',
			translation: 'Prix HT',
			matching: ['Prix HT', 'HT'],
			required: false
		}, {
			name: 'priceWithTaxes',
			translation: 'Prix TTC',
			matching: ['Prix TTC', 'TTC','priceWithTax'],
			required: false
		}, {
			name: 'vat',
			translation: 'TVA',
			matching: ['TVA','vat'],
			required: false
		}, {
			name: 'ean13',
			translation: 'ean13',
			matching: ['ean13'],
			required: false
		}];
	constructor(establishmentUid: VisualScopedUuid<UuidScopeEstablishment>, billingRegions: BillingRegionApiOut[]) {
		this.establishmentUid = establishmentUid;
		this.billingRegions = billingRegions;

		const specificState = GetInstance(SpecificState);
		if(!specificState.haveReservitApp()) {
			ProductImportExportHelper.csvImportColumns.push({
				name: 'billingAccountCode',
				translation: 'Compte comptable',
				matching: ['Compte comptable', 'Compte'],
				required: false
			});
		}
	}

	@AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
	@AutoWired(BillingAccountRepository) accessor billingAccountRepository!: BillingAccountRepository;
	@AutoWired(SpecificState) accessor specificState!: SpecificState;

	entityBuilder(bindings: Record<string, string>, importedLines: ParsedCsv, products: ProductApiOut[], type: ProductType | null = null): EntityBuilderReturn {
		const result: {
			uid: Uuid | null,
			entity: Entity
		}[] = [];
		let lineNumber = 1;

		let updatedLines = [];

		const errors: ColumnBindingError[] = [];
		for (let line of importedLines.lines) {
			lineNumber++;
			let isOneError: boolean = false;

			if (bindings['name'] === undefined || line[bindings['name']] === undefined || line[bindings['name']] === '') continue;
			else if(
				(line[bindings['vat']] === undefined || line[bindings['vat']] === '') &&
				(line[bindings['priceWithoutTaxes']] === undefined || line[bindings['priceWithoutTaxes']] === '') &&
				(line[bindings['priceWithTaxes']] === undefined || line[bindings['priceWithTaxes']] === '')
			) {
				continue;
			}

			let updatedLine = {...line};

			let productWithUid: ProductApiOut | null = null;
			let uid = line[bindings['uid']];
			if (uid !== '' && uid !== undefined) {
				// S'il y a un uid on vérifie qu'un produit existe bien avec cet uid.
				if (UuidUtils.isVisual(uid, uuidScopeProductProduct)) {
					productWithUid = ProductUtils.searchByUid(uid, products);
					if (!productWithUid) {
						updatedLine[bindings['name']] = updatedLine[bindings['name']] + ' <Non importé>';
						updatedLines.push(updatedLine);
						continue;
					}
				} else {
					updatedLine[bindings['name']] = updatedLine[bindings['name']] + ' <Non importé : uid invalide>';
					updatedLines.push(updatedLine);
					continue;
				}
			} else {
				// S'il n'y a PAS d'uid on vérifie qu'aucun produit n'a le même nom.
				let product = ProductUtils.searchByName(line[bindings['name']], products);
				if (product) {
					updatedLine[bindings['name']] = updatedLine[bindings['name']] + ' <Non importé : nom déjà existant>';
					updatedLines.push(updatedLine);
					continue;
				}
			}

			if(line[bindings['name']].length > 128) errors.push(new ColumnBindingError(`Le nom (${line[bindings['name']]}) est trop long. Il doit faire moins de 128 caractères (actuellement ${line[bindings['name']].length})`, bindings['name'], lineNumber - 1));

			const matchingBillingRegion = this.billingRegions.find((region) => {
				return region.taxes.length === 1 && (region.taxes[0].percent / Constants.PERCENT_MULTIPLIER * 100) === parseFloat(line[bindings['vat']].replace(',', '.'));
			});

			if (!matchingBillingRegion) {
				errors.push(new ColumnBindingError(`Une ligne contient une TVA non autorisée (${line[bindings['vat']]}%)`, bindings['vat'], lineNumber - 1));
				continue;
			}

			const productModel = new ProductModel();

			let priceWithoutTax: number|null = line[bindings['priceWithoutTaxes']] ? Math.round(parseFloat(line[bindings['priceWithoutTaxes']].replace(',', '.')) * 100) : null;
			let priceWithTax: number|null = line[bindings['priceWithTaxes']] ? Math.round(parseFloat(line[bindings['priceWithTaxes']].replace(',', '.')) * 100) : null;

			if(priceWithoutTax === null && priceWithTax === null) {
				errors.push(new ColumnBindingError(`Une ligne ne contient aucun prix`, bindings['priceWithTax'], lineNumber - 1));
				continue;
			} else if(priceWithoutTax !== null && priceWithTax === null) {
				priceWithTax = productModel.computePriceWithTax(priceWithoutTax, matchingBillingRegion.taxes);
				if(bindings['priceWithTaxes']) updatedLine[bindings['priceWithTaxes']] = (priceWithTax / 100) + '';
			} else if(priceWithTax !== null && priceWithoutTax === null) {
				priceWithoutTax = productModel.computePriceWithoutTaxAndTaxesWithPriceWithTax(priceWithTax, matchingBillingRegion.taxes).priceWithoutTaxes;
				if(bindings['priceWithoutTaxes']) updatedLine[bindings['priceWithoutTaxes']] = (priceWithoutTax / 100) + '';
			}

			if (!productModel.checkPriceWithAndWithoutTaxesAreWithinRange(priceWithoutTax!, priceWithTax!, matchingBillingRegion.taxes)) {
				errors.push(new ColumnBindingError(`Une ligne contient des prix invalides : le prix HT ne correspond pas au TTC (${MoneyFilter(priceWithoutTax!)} HT devrait faire ${MoneyFilter(productModel.computePriceWithTax(priceWithoutTax!, matchingBillingRegion.taxes))} TTC, ${MoneyFilter(priceWithTax!)} TTC renseigné ligne ${lineNumber})`, bindings['priceWithTaxes'], lineNumber - 1));
			}

			const priceApi = new ProductPriceApi({
				billingCategoryRegionUid: matchingBillingRegion.uid,
				withTaxes: priceWithTax!,
				withoutTaxes: priceWithoutTax!,
			});

			let groups: ProductGroupData[] = [];
			if (productWithUid) {
				groups = productWithUid.lastRevision.groups.map((group)=>{return new ProductGroupData(group)});
			}

			let groupIngredient = groups.filter((g) => {
				return g.name === 'ingredients';
			})[0];

			if (products) {
				let items = [{
					name: 'i1_name',
					quantity: 'i1_quantity'
				}, {
					name: 'i2_name',
					quantity: 'i2_quantity'
				}, {
					name: 'i3_name',
					quantity: 'i3_quantity'
				}];

				for (let item of items) {
					let cellNameContent = line[bindings[item.name]];
					let cellQuantityContent = line[bindings[item.quantity]];
					if (cellNameContent !== '' && cellNameContent !== undefined && cellQuantityContent !== '' && cellQuantityContent !== undefined) {
						let ingredients = products.filter((p) => {
							return p.lastRevision.type === ProductType.FOOD_INGREDIENT
						});
						if(ingredients.length===0){
							throw 'no_ingredients'
						}
						let product = ProductUtils.searchByName(cellNameContent, ingredients);
						if (product) {
							if (!groupIngredient) {
								groupIngredient = new ProductGroupData();
								groupIngredient.name = 'ingredients';
								groupIngredient.minQuantity = 0;
								groupIngredient.maxQuantity = null;
								groupIngredient.items = [];
								groups.push(groupIngredient);
							}

							let groupItemData = new ProductGroupItemData();
							if(groupItemData.prices===null)groupItemData.prices = [new ProductRevisionPriceForItemData({
								withoutTaxes:0,
								withTaxes:0,
								toCountry: CountryEnum.FRA
							})];
							groupItemData.minQuantity = parseInt(cellQuantityContent + '');
							groupItemData.maxQuantity = parseInt(cellQuantityContent + '');
							groupItemData.productUid = product.uid;
							groupItemData.defaultQuantity = parseInt(cellQuantityContent + '');
							groupIngredient.items.push(groupItemData);

						} else {
							updatedLine[bindings[item.name]] = updatedLine[bindings[item.name]] + ' <Non trouvé>';
							updatedLine[bindings[item.quantity]] = '-';
							updatedLines.push(updatedLine);
							isOneError = true;
							continue;
						}
					}
				}
			}

			if(isOneError){
				continue;
			}

			result.push({
				uid: line[bindings['uid']] as Uuid || null,
				entity: new ProductRevisionImportApi({
					revisionApiIn: new ProductRevisionApiIn({
						name: line[bindings['name']],
						ean13: line[bindings['ean13']] || null,
						uniquable: type === null,
						prices: [priceApi],
						groups: groups.map((g)=>{return g.toApiIn()}),
						tangible: true,
						type: type
					}),
					billingAccountCode: line[bindings['billingAccountCode']] || null
				})
			});

			updatedLines.push(updatedLine);
		}

		if(errors.length > 0) throw new ColumnBindingsError('', errors);

		return {entities: result, updatedLines: {headers: importedLines.headers, lines: updatedLines}};
	}

	async entitySaver(entities: {
		uid: Uuid | null,
		entity: ProductRevisionImportApi
	}[], billingAccountCodes: BillingAccountCodeApiOut[]) {
		let index: number = 1;

		for (let data of entities) {
			const event = new CustomEvent('processing-entity', {detail: {total: entities.length, current: index, entity: data.entity.revisionApiIn.name}});
			window.dispatchEvent(event);

			if(!this.specificState.haveReservitApp() && data.entity.billingAccountCode) {
				const billingAccountCode = billingAccountCodes.find((code) => code.code === data.entity.billingAccountCode);
				if(billingAccountCode) {
					data.entity.revisionApiIn.billingAccountCodeUid = billingAccountCode.uid;
				} else {
					const createdCode = (await this.billingAccountRepository.callContract('createCode', {establishmentUid: this.establishmentUid}, new BillingAccountCodeCreationApiIn({
						name: data.entity.billingAccountCode,
						code: data.entity.billingAccountCode
					}))).success();
					data.entity.revisionApiIn.billingAccountCodeUid = createdCode.item.uid;
				}
			}

			if (!data.uid) {
				const response = await this.productRepository.callContract('create', {establishmentUid: this.establishmentUid}, data.entity.revisionApiIn);
				if (!response.isSuccess()) {
					const error = response.error();
					console.log(error);
				}
			} else {
				const response = await this.productRepository.callContract('update', {establishmentUid: this.establishmentUid, productUid: data.uid as VisualScopedUuid<UuidScopeProductProduct>}, data.entity.revisionApiIn);
				if (!response.isSuccess()) {
					const error = response.error();
					console.log(error);
				}
			}
			index++;
		}
	}

	async exportProducts(filters: TypedQuerySearch<typeof ProductHttpProductContractSearchConfig>, selectedFileType: ExportFileType = 'xlsx') {
		let allProducts: ProductApiOut[] = [];
		let products: ProductApiOut[] = [];
		do {
			if(products[499]) {
				filters.cursorAfter = products[499].uid
			}

			products = (await this.productRepository.callContract('search', {establishmentUid: this.establishmentUid}, filters)).success();
			allProducts = allProducts.concat(products);
		} while(products.length === 500);

		const jsonProducts = allProducts.map((product) => {
			const taxPercent = this.findBillingRegionWithUid(product.lastRevision.prices[0].billingCategoryRegionUid).taxes[0].percent;

			return {
				'Identifiant unique': product.uid,
				'Nom': product.lastRevision.name,
				'Prix HT': product.lastRevision.prices[0].withoutTaxes / 100,
				'Prix TTC': product.lastRevision.prices[0].withTaxes / 100,
				'TVA': taxPercent / Constants.PERCENT_MULTIPLIER * 100,
				'ean13': product.lastRevision.ean13
			};
		});

		MultiFormatExporter.downloadData([
			{name: 'Identifiant unique', type: "AUTO"},
			{name: 'Nom', type: "AUTO"},
			{name: 'Prix HT', type: "MONEY"},
			{name: 'Prix TTC', type: "MONEY"},
			{name: 'TVA', type: "AUTO"},
			{name: 'ean13', type: "AUTO"},
		], jsonProducts, selectedFileType);
	}

	findBillingRegionWithUid(billingRegionUid: VisualScopedUuid<UuidScopeProductBillingRegion>) {
		const region = this.billingRegions.find((region) => region.uid === billingRegionUid);
		if (!region) throw new Error('missing_billing_region');
		return region;
	}
}