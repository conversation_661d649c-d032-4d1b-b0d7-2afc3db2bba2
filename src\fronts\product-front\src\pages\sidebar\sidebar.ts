import {Component, Vue} from "vue-facing-decorator";
import <PERSON>barStateListener from "../../../../../shared/utils/SidebarStateListener";
import {
	SidebarComponent,
	SidebarHeader,
	SidebarMenu,
	ApplicationsSwitcherComponent,
	SidebarNavigation, ModalOrDrawerComponent
} from "@groupk/vue3-interface-sdk";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {AutoWired, ScopedUuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {PlatformRepository} from "../../../../../shared/repositories/PlatformRepository";
import {
	AppApiOut_type, AppType,
	PermissionBuilder,
	PlatformDescriptorApiOut,
	uuidScopeEstablishment,
	UuidScopeEstablishment
} from "@groupk/mastodon-core";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {ProductPermissions} from "@groupk/mastodon-core";
import {CustomerPermissions} from "@groupk/mastodon-core";
import EstablishmentSwitchComponent
	from "../../../../../shared/components/EstablishmentSwitchComponent/EstablishmentSwitchComponent.vue";
import {AppState} from "../../../../../shared/AppState";
import {AppRepository} from "../../../../../shared/repositories/AppRepository";
import {SpecificState} from "../../SpecificState";

@Component({
	components: {
		sidebar: SidebarComponent,
		'application-switcher': ApplicationsSwitcherComponent,
		'modal-or-drawer': ModalOrDrawerComponent,
		'establishment-switch': EstablishmentSwitchComponent
	},
})
export default class SidebarView extends Vue {
	opened: boolean = false;
	minimized: boolean = false;
	hidden: boolean = true;
	showContactInfos: boolean = false;
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	header: SidebarHeader | undefined = {
		title: "Produit",
		icon: "fa-square-c",
		subtitle: "Votre plateforme",
	};

	menus: SidebarMenu[] = [];

	bottom: SidebarMenu[] = [
		{
			separator: false,
			navigations: [
				{icon: "fa-cog", title: "Paramètres", url: EstablishmentUrlBuilder.buildUrl('/settings'), name: EstablishmentUrlBuilder.buildUrl('/settings')},
				{
				icon: "fa-sign-out-alt", title: "Déconnexion",
				url: '/cas-redirect?disconnect=true',
				name: "disconnect"
			}],
		},
	];

	platformDescriptor!: PlatformDescriptorApiOut|null;
	selectedNavigation: string | undefined = undefined;

	/** UUID **/
	uid: string = randomUUID();

	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(PlatformRepository) accessor platformRepository!: PlatformRepository;
	@AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(SpecificState) accessor specificState!: SpecificState;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.opened = this.sidebarStateListener.openedSidebar;
		this.hidden = this.sidebarStateListener.hiddenSidebar;
		this.minimized = this.sidebarStateListener.minimizedSidebar;
		this.sidebarStateListener.listen(this.stateChanged);
	}

	stateChanged(event: {type: string; value: any}){
		if (event.type === "openedSidebar") {
			this.opened = event.value;
		} else if (event.type === "minimizedSidebar") {
			this.minimized = event.value;
		} else if (event.type === "hiddenSidebar") {
			this.hidden = event.value;
		}
	}

	async mounted() {
		this.selectedNavigation = window.location.pathname;

		this.router.hookOnAsync('newPageLoaded', (data) => {
			if (data.newRoute.location !== null) {
				this.selectedNavigation = window.location.pathname;
			}
			return Promise.resolve(data);
		});

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
			try {
				const establishment = (await this.establishmentRepository.callContract('get', {establishmentUid: this.establishmentUid}, undefined)).success()
				if(this.header) this.header.subtitle = establishment.name;
			} catch(err) {}


			this.bottom[0].navigations[1].url = EstablishmentUrlBuilder.buildUrl('/cas-redirect?disconnect=true');
		}

		this.menus = this.getSidebarItems();
	}


	getSidebarItems(): SidebarMenu[] {
		const menu: SidebarMenu[] = [];
		const gestionNavigations: SidebarNavigation[] = [];
		if(this.appState.hasPermission(PermissionBuilder(ProductPermissions, 'PRODUCT_WRITE'))) {
			gestionNavigations.push({icon: "fa-barcode-read", title: "Inventaires", url: EstablishmentUrlBuilder.buildUrl('/inventories'), name: EstablishmentUrlBuilder.buildUrl('/inventories')});
		}
		if(this.appState.hasPermission(PermissionBuilder(ProductPermissions, 'WORK-CLOCK_LIST'))) {
			gestionNavigations.push({icon: "fa-clock", title: "Horaires", url: EstablishmentUrlBuilder.buildUrl('/work-clock-summary'), name: EstablishmentUrlBuilder.buildUrl('/work-clock-summary')});
		}

		if(this.appState.hasPermission(PermissionBuilder(CustomerPermissions, 'CUSTOMER_LIST'))) {
			gestionNavigations.push({icon: "fa-circle-user", title: "Clients", url: EstablishmentUrlBuilder.buildUrl('/customers'), name: EstablishmentUrlBuilder.buildUrl('/customers')});
		}

		gestionNavigations.push({icon: "fa-folder", title: "Fichiers", url: EstablishmentUrlBuilder.buildUrl('/uploads'), name: EstablishmentUrlBuilder.buildUrl('/uploads')});

		// if(this.appState.hasPermission(MastodonPermissions.applicationId, 'RESELLER')) {
		// 	menu.push({
		// 		separator: true,
		// 		title: "Espace revendeur",
		// 		navigations: [{
		// 			icon: "fa-screwdriver-wrench",
		// 			title: "Configuration",
		// 			url: EstablishmentUrlBuilder.buildUrl('/reseller'),
		// 			name: EstablishmentUrlBuilder.buildUrl('/reseller')
		// 		}]
		// 	});
		// }

		if(this.getAppsForType(AppType.RESERVIT).length > 0) {
			gestionNavigations.push({icon: "fa-cog", title: "Reservit", url: EstablishmentUrlBuilder.buildUrl('/reservit'), name: EstablishmentUrlBuilder.buildUrl('/reservit')});
		}

		gestionNavigations.push({icon: "fa-plus", title: "Intégrations", url: EstablishmentUrlBuilder.buildUrl('/integrations'), name: EstablishmentUrlBuilder.buildUrl('/integrations')});

		return menu.concat([{
			separator: true,
			title: "Produit",
			navigations: [
				{
					icon: "fa-grid-2",
					title: "Produits",
					url: EstablishmentUrlBuilder.buildUrl('/products'),
					name: [EstablishmentUrlBuilder.buildUrl('/products'), EstablishmentUrlBuilder.buildUrl('/'), EstablishmentUrlBuilder.buildUrl('')]
				},{
					icon: "fa-rectangle-history-circle-plus",
					title: "Ingrédients",
					url: EstablishmentUrlBuilder.buildUrl('/ingredients'),
					name: EstablishmentUrlBuilder.buildUrl('/ingredients')
				},
				// {icon: "fa-store", title: "Catégories", url: EstablishmentUrlBuilder.buildUrl('/categories'), name: EstablishmentUrlBuilder.buildUrl('/categories')},
				{icon: "fa-store", title: "Points de vente", url: EstablishmentUrlBuilder.buildUrl('/physical-sales-points'), name: EstablishmentUrlBuilder.buildUrl('/physical-sales-points')},
				// {icon: "fa-badge-percent", title: "Réductions", url: EstablishmentUrlBuilder.buildUrl('/discount-contexts'), name: EstablishmentUrlBuilder.buildUrl('/discount-contexts')},
			],
		},
			{
				separator: true,
				title: "Ventes",
				navigations: [
					{icon: "fa-bag-shopping", title: "Commandes", url: EstablishmentUrlBuilder.buildUrl('/orders'), name: EstablishmentUrlBuilder.buildUrl('/orders')},
					{icon: "fa-chart-simple", title: "Statistiques", url: EstablishmentUrlBuilder.buildUrl('/statistics'), name: EstablishmentUrlBuilder.buildUrl('/statistics')},
					// {icon: "fa-circle-user", title: "Clients", url: EstablishmentUrlBuilder.buildUrl('/customers'), name: EstablishmentUrlBuilder.buildUrl('/customers')},
				],
			},
			{
				separator: true,
				title: "Gestion",
				navigations: gestionNavigations,
		}]);
	}


	async loadPlatform() {
		if(!this.platformDescriptor) this.platformDescriptor = (await this.platformRepository.callContract('get', undefined, undefined)).success();
	}

	getAppsForType(type: AppType) {
		return this.specificState.requireApps().filter((app) => app.type === type);
	}

	navigationClicked(_value: string) {}
}
