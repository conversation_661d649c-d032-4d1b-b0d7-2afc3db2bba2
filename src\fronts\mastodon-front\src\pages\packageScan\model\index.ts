/**
 * Baserow Entity Classes for Package Scanning
 * 
 * This module exports entity classes that correspond to the Baserow database tables
 * used in the package scanning functionality.
 * 
 * Database: 🔒Colis (ID: 446)
 * API Base URL: https://baserow-api.lab.weecop.fr
 * 
 * Tables:
 * - Package (ID: 1731) - Main package information
 * - Objects (ID: 1732) - Objects within packages
 * - ObjectNames (ID: 1733) - Object naming/categorization
 */

import {PackageEntity} from "./PackageEntity";
import {ObjectEntity} from "./ObjectEntity";
import {ObjectNamesEntity, BRAND_OPTIONS, BRAND_LABELS} from "./ObjectNamesEntity";

// Export entity classes
export {PackageEntity, ObjectEntity, ObjectNamesEntity};

// Export brand constants
export {BRAND_OPTIONS, BRAND_LABELS};

// Type definitions for easier usage
export type BaserowPackage = PackageEntity;
export type BaserowObject = ObjectEntity;
export type BaserowObjectNames = ObjectNamesEntity;
