import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, FetchError, genericFetch} from "@groupk/horizon2-core";
import {BarcodeScannerManager} from "../../BarcodeScannerManager";
import {ObjectEntity} from "../../model";

@Component({
    components: {},
    emits: ['close', 'created']
})
export default class ObjectFormComponent extends Vue {
    @Prop({required: true}) editingObject!: ObjectEntity;

    comment: string = '';
    creating: boolean = false;

    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;

    mounted() {
        this.comment = this.editingObject.comment;
        this.barcodeScannerManager.customCallback = null;
    }

    async save() {
        this.creating = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects',
            method: 'PATCH',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "records": [
                    {
                        "id": this.editingObject.id,
                        "fields": {
                            "comment": this.comment
                        }
                    }
                ]
            })
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.$emit('updated', json.records[0]);
        }

        this.creating = false;
        this.close();
    }

    close() {
        this.$emit('close');
    }
}