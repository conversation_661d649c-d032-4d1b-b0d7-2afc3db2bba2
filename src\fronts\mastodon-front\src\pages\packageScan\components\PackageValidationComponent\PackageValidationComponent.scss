.package-validation-component {
    position: fixed;
    inset: 0;
    z-index: 1000;
    height: 100%;
    background: #f0f2f3;
    overflow: auto;

    .loading-container {
        margin: 0;
        height: 100%;
    }

    .container {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 20px;
        max-width: 700px;
        margin: auto;

        h3 {
            font-size: 18px;
            font-weight: 600;
            margin: 10px 0 0 0;
        }

        .actions {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 20px;

            .action-group {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 4px;

                .action {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    background: white;
                    border-radius: 20px;
                    cursor: pointer;
                    width: 80%;
                    aspect-ratio: 1/1;

                    i {
                        font-size: 22px;
                    }
                }

                .text {
                    font-size: 14px;
                }
            }
        }

        .objects {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .object {
                display: flex;
                flex-direction: column;
                gap: 6px;
                background: white;
                padding: 20px;
                border-radius: 20px;

                .top {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    .name {
                        flex-grow: 2;
                    }

                    .images {
                        display: flex;
                        gap: 10px;

                        i {
                            color: grey;
                            font-size: 18px;
                        }
                    }
                }

                .bottom {
                    color: grey;
                }

                .fa-ellipsis-vertical {
                    margin-right: 5px;
                }
            }
        }
    }

    .scanning-modal {
        position: fixed;
        inset: 0;
        z-index: 1001;
        background: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;

        &.already-in {
            background: rgba(255, 0, 0, 0.2);
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            max-width: 80%;
            box-sizing: border-box;

            .title {
                font-size: 18px;
                font-weight: 600;
                text-align: center;
            }

            .buttons {
                display: flex;
                flex-direction: column;
                margin-top: 0;

                .button {
                    padding: 20px;
                }
            }
        }
    }

    .scan-state {
        position: fixed;
        inset: 0;
        z-index: 1001;
        display: flex;
        align-items: center;
        justify-content: center;

        &.success {
            background: rgba(2, 232, 2, 0.4);
        }

        &.error {
            background: rgba(255, 0, 0, 0.4);
        }
    }

    .done {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20px;
        flex-grow: 2;
        font-size: 20px;
        background: white;
        padding: 40px;
        border-radius: 20px;

        .button {
            font-size: 18px;
            padding: 20px 25px;
            border-radius: 20px;
        }
    }

    .scanning-modal {
        position: fixed;
        inset: 0;
        z-index: 1001;
        background: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;

        &.already-in {
            background: rgba(255, 0, 0, 0.2);
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            max-width: 80%;
            box-sizing: border-box;

            .title {
                font-size: 18px;
                font-weight: 600;
                text-align: center;
            }

            .buttons {
                display: flex;
                flex-direction: column;
                margin-top: 0;

                .button {
                    padding: 20px;
                }
            }
        }
    }

    .object-image {
        position: fixed;
        inset: 0;
        z-index: 1002;
        width: 100%;
        height: 100%;
        object-fit: contain;
        background: black;
    }
}