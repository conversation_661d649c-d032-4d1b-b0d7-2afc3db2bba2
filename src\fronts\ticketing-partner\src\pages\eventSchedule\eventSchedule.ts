import {Component, Ref, Vue} from "vue-facing-decorator";
import {
	AutoWired, ScopedUuid, UuidUtils, VisualScopedUuid,
} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import FullWidthCalendarComponent, {
	CalendarEvent
} from "../../components/FullWidthCalendarComponent/FullWidthCalendarComponent.vue";
import {
	DropdownButtonAction,
	DropdownButtonComponent,
	DropdownComponent,
	DropdownValue,
	FormModalOrDrawerComponent
} from "@groupk/vue3-interface-sdk";
import {StockTemporalRepository} from "../../../../../shared/repositories/StockTemporalRepository";
import {
	EventApiOut,
	MetadataApiIn_type, MetadataApiOut_type,
	MetadataDescriptorApiOut, MetadataHttpContract,
	OrderApiOut,
	ProductApiOut,
	ProductRevisionApiOut,
	StockTemporalApiOut,
	StockTemporalModel,
	StockTemporalUsageListApiOut,
	TemplateTicketApiOut,
	TicketApiOut,
	TicketBatchApiOut,
	UuidScopeCustomer_customer,
	uuidScopeMetadata_descriptor,
	uuidScopeProduct_event,
	UuidScopeProduct_event,
	UuidScopeProduct_templateTicket,
	UuidScopeProductProductRevision,
} from "@groupk/mastodon-core";
import {randomUUID, Router} from "@groupk/horizon2-front";
import {EventRepository} from "../../../../../shared/repositories/EventRepository";
import {ProductRepository} from "../../../../../shared/repositories/ProductRepository";
import {AppState} from "../../../../../shared/AppState";
import TicketImportComponent from "../../components/TicketImportComponent/TicketImportComponent.vue";
import StockTemporalFormComponent from "../../components/StockTemporalFormComponent/StockTemporalFormComponent.vue";
import {UuidScopeProduct_stockTemporal} from "@groupk/mastodon-core";
import DateUtils from "../../../../../shared/utils/DateUtils";
import {TemplateTicketRepository} from "../../../../../shared/repositories/TemplateTicketRepository";
import {TemplateTicketLabelRepository} from "../../../../../shared/repositories/TemplateTicketLabelRepository";
import {
	TemplateTicketLabelApiOut,
	UuidScopeProduct_templateTicketLabel
} from "@groupk/mastodon-core";
import TicketDropdownActionsComponent
	from "../../components/TicketDropdownActionsComponent/TicketDropdownActionsComponent.vue";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {MetadataDescriptorRepository} from "../../../../../shared/repositories/MetadataDescriptorRepository";
import {UuidScopeMetadata_descriptor} from "@groupk/mastodon-core";
import TicketCsvImportComponent from "../../components/TicketCsvImportComponent/TicketCsvImportComponent.vue";
import {TicketRepository} from "../../../../../shared/repositories/TicketRepository";
import MetadataInputComponent
	from "../../../../../shared/components/MetadataInputComponents/MetadataInputComponent/MetadataInputComponent.vue";
import {MetadataUtils} from "../../../../../shared/utils/MetadataUtils";
import {MetadataRepository} from "../../../../../shared/repositories/MetadataRepository";
import {AppBus} from "../../AppBus";
import {translateResponseError} from "../../../../../shared/RepositoryExtensions";
import OrderBuyerComponent from "./OrderBuyerComponent.vue";
import BatchBuyerComponent from "./BatchBuyerComponent.vue";
import CalendarExportComponent from "./CalendarExportComponent.vue";

@Component({
	components: {
		'full-width-calendar': FullWidthCalendarComponent,
		'ticket-import': TicketImportComponent,
		'stock-temporal-form': StockTemporalFormComponent,
		'dropdown': DropdownComponent,
		'dropdown-button': DropdownButtonComponent,
		'ticket-dropdown-actions': TicketDropdownActionsComponent,
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'ticket-csv-import': TicketCsvImportComponent,
		'metadata-input': MetadataInputComponent,
		'order-buyer': OrderBuyerComponent,
		'batch-buyer': BatchBuyerComponent,
		'calendar-export': CalendarExportComponent,
	}
})
export default class eventSchedule extends Vue {
	event!: EventApiOut;
	products: ProductApiOut[] = [];
	temporalStocks: StockTemporalApiOut[] = [];
	templateTickets: TemplateTicketApiOut[] = [];
	metadataDescriptors: MetadataDescriptorApiOut[] = [];
	templateTicketLabels: TemplateTicketLabelApiOut[] = [];

	events: CalendarEvent[] = [];
	usages!: StockTemporalUsageListApiOut;

	showTemporalData: Date|null = null;
	currentStockTemporal!: StockTemporalApiOut;
	showTicketImportModal: boolean = false;
	showExportModal: boolean = false;

	currentMonday: Date|null = null;

	editingStockTemporal: StockTemporalApiOut|null = null;
	editingBatchMetadata: {batch: TicketBatchApiOut, metadataDescriptor: MetadataDescriptorApiOut, metadata: MetadataApiIn_type}|null = null;
	showStockModal: boolean = false;
	updatingMetadata: boolean = false;
	metadataUpdateError: string|null = null;
	displayCanceledOrders: boolean = false;
	loading: boolean = true;

	@Ref() calendar!: FullWidthCalendarComponent;

	@AutoWired(EventRepository) accessor eventRepository!: EventRepository;
	@AutoWired(StockTemporalRepository) accessor stockTemporalRepository!: StockTemporalRepository;
	@AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
	@AutoWired(TemplateTicketRepository) accessor templateTicketRepository!: TemplateTicketRepository;
	@AutoWired(MetadataDescriptorRepository) accessor metadataDescriptorRepository!: MetadataDescriptorRepository;
	@AutoWired(MetadataRepository) accessor metadataRepository!: MetadataRepository;
	@AutoWired(TemplateTicketLabelRepository) accessor templateTicketLabelRepository!: TemplateTicketLabelRepository;
	@AutoWired(TicketRepository) accessor ticketRepository!: TicketRepository;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(AppBus) accessor appBus!: AppBus;
	@AutoWired(Router) accessor router!: Router;

	beforeMount() {
		this.sidebarStateListener.setMinimizedSidebar(false);
	}

	get currentSunday() {
		if(!this.currentMonday) return null;
		const sunday = new Date(this.currentMonday);
		sunday.setDate(this.currentMonday.getDate() + 6)
		return sunday;
	}

	async mounted() {
		const previousZoom = localStorage.getItem('stock-calendar-zoom');
		if(previousZoom) document.documentElement.style.setProperty('--full-width-calendar-zoom', ''+(parseFloat(previousZoom) - 0.1));

		this.temporalStocks = (await this.stockTemporalRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success().list;

		const url = new URL(window.location.href);
		if(url.searchParams.has('stockUid')) {
			this.currentStockTemporal = this.temporalStocks.find((stock) => UuidUtils.visualToScoped(stock.uid) === url.searchParams.get('stockUid')!) || this.temporalStocks[0];
		} else {
			this.currentStockTemporal = this.temporalStocks[0];
		}

		if(this.currentStockTemporal) {
			url.searchParams.set('stockUid', UuidUtils.visualToScoped(this.currentStockTemporal.uid));
			window.history.replaceState({}, '', url);
		}

		let regexMatch = this.router.lastRouteRegexMatches;
		if (regexMatch && regexMatch[2]) {
			const eventUid = UuidUtils.scopedToVisual(regexMatch[2] as ScopedUuid<UuidScopeProduct_event>, uuidScopeProduct_event);

			this.event = (await this.eventRepository.callContract('getOne', {
				establishmentUid: this.appState.requireUrlEstablishmentUid(),
				eventUid: eventUid
			}, undefined)).success();

			this.templateTickets = (await this.templateTicketRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid(), eventUid: eventUid}, undefined)).success();
			this.templateTicketLabels = (await this.templateTicketLabelRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success().list;
			this.metadataDescriptors = (await this.metadataDescriptorRepository.callContract('listDescriptors', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		}

		this.router.updatePageWithDescriptor({
			pageTitle: 'Disponibilités - ' + this.event.name + ' - Billetterie'
		});

		this.loading = false;
	}

	setCurrentStockTemporal(stockTemporal: StockTemporalApiOut) {
		this.currentStockTemporal = stockTemporal;

		const url = new URL(window.location.href);
		url.searchParams.set('stockUid', UuidUtils.visualToScoped(this.currentStockTemporal.uid));
		window.history.replaceState({}, '', url);

	}

	async computeEventsForWeek(currentMonday: Date) {
		const monday = new Date(currentMonday);
		monday.setHours(0);
		monday.setMinutes(0);
		monday.setSeconds(0);
		monday.setMilliseconds(0);

		const sunday = new Date(currentMonday);
		sunday.setDate(currentMonday.getDate() + 6);
		sunday.setHours(23);
		sunday.setMinutes(59);
		sunday.setSeconds(59);
		sunday.setMilliseconds(999);

		this.usages = (await this.stockTemporalRepository.callContract('getUsageList', {
			establishmentUid: this.appState.requireUrlEstablishmentUid(),
			stockUid: this.currentStockTemporal.uid,
			startDatetime: monday.toISOString(),
			endDatetime: sunday.toISOString(),
		}, undefined)).success();

		this.events = StockTemporalModel.computeStockTemporalAvailabilities(
			this.currentStockTemporal.ruleList,
			this.currentStockTemporal.eventList,
			this.usages.list,
			monday.toISOString(),
			sunday.toISOString(),
			{filterZeroStock: false}
		).map((data) => {
			const endDate = new Date(data.datetime);
			endDate.setTime(endDate.getTime() + ((data.indicativeDurationMinute ?? 0) * 60 * 1000));

			const matchingUsageList = this.usages.list.filter((usage) => usage.datetime === data.datetime);
			const matchingTickets: TicketApiOut[] = [];
			const labels: {uid: VisualScopedUuid<UuidScopeProduct_templateTicketLabel>, icon: string, color: string, text: string}[] = [];

			for(const ticket of this.usages.ticketList) {
				if(matchingUsageList.map((usage) => usage.targetUid).includes(ticket.uid)) {
					matchingTickets.push(ticket);
				}
			}

			for(const order of this.usages.orderList) {
				for(const ticketPurchaseLink of (order.ticketPurchaseLinkList ?? [])) {
					if(matchingUsageList.map((usage) => usage.targetUid).includes(ticketPurchaseLink.purchaseItemUid)) {
						const ticket = this.usages.ticketList.find((ticket) => ticket.uid === ticketPurchaseLink.ticketUid);
						if(!ticket) throw new Error('missing_ticket');
						matchingTickets.push(ticket);
					}
				}
			}

			for(const ticket of matchingTickets.filter((ticket) => !ticket.disabled)) {
				const templateTicket = this.requireTemplateTicketWithUid(ticket.templateTicketUid);
				for(const labelUid of templateTicket.labelUidList) {
					if(labels.find((label) => label.uid === labelUid)) continue;

					const label = this.templateTicketLabels.find((label) => label.uid === labelUid);
					if(!label) throw new Error('missing_label');
					labels.push({
						uid: label.uid,
						color: label.color,
						icon: label.symbol,
						text: label.name
					});
				}
			}

			return {
				uid: randomUUID(),
				title: 'Event',
				description: 'Dispo. : ' + data.remainingStock + '/' + data.initialStock,
				remainingStock: data.remainingStock,
				initialStock: data.initialStock,
				indicativeDurationMinute: data.indicativeDurationMinute,
				rawDatetime: data.datetime,
				startDate: new Date(data.datetime),
				endDate: endDate,
				tickets: matchingTickets,
				labels: labels
			} satisfies CalendarEvent;
		});

	}

	getTemporalStockDropdownValues(): DropdownValue[] {
		return this.temporalStocks.map((temporalStock) => {
			return {
				value: temporalStock.uid,
				name: temporalStock.name
			}
		});
	}

	updatedStockTemporal(updatedStockTemporal: StockTemporalApiOut) {
		const index = this.temporalStocks.findIndex((stockTemporal) => stockTemporal.uid === updatedStockTemporal.uid);
		if(index !== -1) this.temporalStocks.splice(index, 1, updatedStockTemporal);
		if(this.currentStockTemporal.uid === updatedStockTemporal.uid) this.currentStockTemporal = updatedStockTemporal;

		if(this.currentMonday) this.computeEventsForWeek(this.currentMonday);
	}

	async orderDropdownClicked(batchOrOrder: TicketBatchApiOut|OrderApiOut, action: DropdownButtonAction) {
		if(action.id === 'cancel-all') {
			const tickets: TicketApiOut[] = this.usages.ticketList.filter((ticket) => ticket.groupUid === batchOrOrder.uid);
			for(const ticket of tickets) {
				try {
					await this.ticketRepository.callContract('disable', {establishmentUid: this.appState.requireUrlEstablishmentUid(), ticketUid: ticket.uid}, undefined);
					ticket.disabled = true;
				} catch(err) {}
			}
		} else if(UuidUtils.isVisual<UuidScopeMetadata_descriptor>(action.id, uuidScopeMetadata_descriptor) && batchOrOrder instanceof TicketBatchApiOut) {
			const metadataDescriptor = this.requireMetadataDescriptorWithUid(action.id);

			const currentBatchMetadata: MetadataApiOut_type|null = batchOrOrder.metadataList.find((metadata) => metadata.descriptorUid === metadataDescriptor.uid) || null;

			this.editingBatchMetadata = {
				batch: batchOrOrder,
				metadataDescriptor: this.requireMetadataDescriptorWithUid(action.id),
				metadata: MetadataUtils.createMetadataApiIn(metadataDescriptor, currentBatchMetadata)
			}

			this.editingBatchMetadata.metadata.uid = currentBatchMetadata?.uid;
		}
	}

	async updateBatchMetadata() {
		if(!this.editingBatchMetadata || !this.editingBatchMetadata.metadata.uid) return;

		this.metadataUpdateError = null;
		this.updatingMetadata = true;

		const response = await this.metadataRepository.callContract('patch', {establishmentUid: this.appState.requireUrlEstablishmentUid(), metadataUid: this.editingBatchMetadata.metadata.uid}, this.editingBatchMetadata.metadata);

		if(response.isSuccess()) {
			const updatedMetadata = response.success();
			const index = this.editingBatchMetadata.batch.metadataList.findIndex((metadata) => metadata.uid === updatedMetadata.uid);

			if(index !== -1) {
				this.editingBatchMetadata.batch.metadataList.splice(index, 1, updatedMetadata);
			}

			this.editingBatchMetadata = null;
		} else {
			this.metadataUpdateError = translateResponseError<typeof MetadataHttpContract, 'patch'>(response, {
				invalid_data: undefined
			});
		}

		this.updatingMetadata = false;
	}

	load() {
		return {
			pageTitle: 'Disponibilités - Billetterie'
		};
	}

	previousWeek() {
		this.calendar.previousWeek();
	}

	nextWeek() {
		this.calendar.nextWeek();
	}

	currentWeek() {
		this.calendar.currentWeek();
	}

	zoomOut() {
		const value = parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--full-width-calendar-zoom'));
		if(value > 0.6) document.documentElement.style.setProperty('--full-width-calendar-zoom', ''+(value - 0.1));
		localStorage.setItem('stock-calendar-zoom', `${value}`);
	}

	zoomIn() {
		const value = parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--full-width-calendar-zoom'));
		if(value < 1.2) document.documentElement.style.setProperty('--full-width-calendar-zoom', ''+(value + 0.1));
		localStorage.setItem('stock-calendar-zoom', `${value}`)
	}

	createdStockTemporal(stockTemporal: StockTemporalApiOut) {
		const index = this.temporalStocks.findIndex((existingStockTemporal) => stockTemporal.uid === existingStockTemporal.uid);
		if(index === -1) {
			this.temporalStocks.push(stockTemporal);
			this.currentStockTemporal = stockTemporal;
		}
		else this.temporalStocks.splice(index, 1, stockTemporal);
	}

	requireStockTemporalWithUid(stockUid: VisualScopedUuid<UuidScopeProduct_stockTemporal>): StockTemporalApiOut {
		const stock = this.temporalStocks.find((stock) => stock.uid === stockUid);
		if(!stock) throw new Error('missing_stock');
		return stock;
	}

	getOrdersOnTemporal(temporalDatetime: Date, canceled: boolean = false) {
		const formattedDatetime = this.formatDateToTemporalDateTime(temporalDatetime);

		const orders: OrderApiOut[] = [];
		for(const order of this.usages.orderList) {
			let matched: boolean = false;

			for(const ticketPurchaseLink of (order.ticketPurchaseLinkList ?? [])) {
				const usage = this.usages.list.find((usage) => usage.targetUid === ticketPurchaseLink.ticketUid);
				if(usage && usage.datetime === formattedDatetime) {
					const everyOrderTickets = this.getOrderTickets(order);

					if (everyOrderTickets.length > 0) {
						if (canceled) {
							// If canceled = true, include order if ALL tickets are disabled
							if (everyOrderTickets.every((ticket) => ticket.disabled)) {
								orders.push(order);
							}
						} else {
							// If canceled = false, include order if ANY ticket is NOT disabled
							if (everyOrderTickets.some((ticket) => !ticket.disabled)) {
								orders.push(order);
							}
						}
					}

					matched = true;
					break;
				}
			}
			if(matched) continue;

			for(const purchase of order.purchases) {
				for(const item of purchase.items) {
					if(this.usages.list.findIndex((usage) => {
						return usage.datetime === formattedDatetime && usage.targetUid === item.uid
					}) !== -1) {
						const tickets = this.getOrderTickets(order);

						// Apply the same logic as getBatchesOnTemporal
						if (tickets.length > 0) {
							if (canceled) {
								// If canceled = true, include order if ALL tickets are disabled
								if (tickets.every((ticket) => ticket.disabled)) {
									orders.push(order);
								}
							} else {
								// If canceled = false, include order if ANY ticket is NOT disabled
								if (tickets.some((ticket) => !ticket.disabled)) {
									orders.push(order);
								}
							}
						}

						matched = true;
					}
					if(matched) break;
				}
				if(matched) break;
			}
		}

		return orders;
	}

	getOrderTickets(order: OrderApiOut) {
		return this.usages.ticketList.filter((ticket) => ticket.groupUid === order.uid);
	}

	getBatchesOnTemporal(temporalDatetime: Date, canceled: boolean = false) {
		const formattedDatetime = this.formatDateToTemporalDateTime(temporalDatetime);

		const relevantUsageTargets = new Set(
			this.usages.list
				.filter(usage => usage.datetime === formattedDatetime)
				.map(usage => usage.targetUid)
		);

		return this.usages.ticketBatchList.filter(batch => {
			// Get all tickets for this batch that match the temporal datetime
			const batchTickets = this.usages.ticketList.filter(ticket =>
				ticket.groupUid === batch.uid &&
				relevantUsageTargets.has(ticket.uid)
			);

			// If no tickets found for this batch, exclude it
			if (batchTickets.length === 0) {
				return false;
			}

			if (canceled) {
				// If canceled = true, return batch if ALL tickets are disabled
				return batchTickets.every(ticket => ticket.disabled);
			} else {
				// If canceled = false, return batch if ANY ticket is NOT disabled
				return batchTickets.some(ticket => !ticket.disabled);
			}
		});
	}

	formatDateToTemporalDateTime(date: Date): string {
		return DateUtils.formatDateIsoFormat(date.toISOString()) + 'T' + DateUtils.formatDateHour(date.toISOString(), ':') + ':00.000';
	}

	requireProductRevision(uid: VisualScopedUuid<UuidScopeProductProductRevision>): ProductRevisionApiOut {
		const revision = this.usages.productRevisionList.find((revision) => revision.uid === uid);
		if (!revision) throw new Error('missing_revision');
		return revision;
	}

	requireTemplateTicketWithUid(templateTicketUid: VisualScopedUuid<UuidScopeProduct_templateTicket>) {
		const templateTicket = this.templateTickets.find((templateTicket) => templateTicket.uid === templateTicketUid);
		if (!templateTicket) throw new Error('missing_template_ticket');
		return templateTicket;
	}
	
	getCustomerUrl(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>) {
		return EstablishmentUrlBuilder.buildUrl('/customers?uid=' + UuidUtils.visualToUuid(customerUid));
	}

	requireMetadataDescriptorWithUid(descriptorUid: VisualScopedUuid<UuidScopeMetadata_descriptor>) {
		const descriptor = this.metadataDescriptors.find((descriptor) => descriptor.uid === descriptorUid);
		if (!descriptor) throw new Error('missing_descriptor');
		return descriptor;
	}

}