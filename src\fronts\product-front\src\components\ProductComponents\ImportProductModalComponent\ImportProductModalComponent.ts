import {Component, Prop, Vue} from "vue-facing-decorator";
import {ImportColumn, ModalOrDrawerComponent, TableImportComponent} from "@groupk/vue3-interface-sdk";
import {
	ProductRevisionApiIn,
	ProductApiOut,
	BillingRegionApiOut,
	ProductType,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	ProductHttpBillingRegionContract, ProductHttpProductContract, BillingAccountCodeApiOut
} from "@groupk/mastodon-core";
import {AutoWired, VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import {ProductImportExportHelper} from "../../../../../../shared/utils/ProductImportExportHelper";
import {ProductRepository} from "../../../../../../shared/repositories/ProductRepository";
import {BillingRegionRepository} from "../../../../../../shared/repositories/BillingRegionRepository";

export function ImportProductModalComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]) {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		ProductHttpBillingRegionContract.list,
		ProductHttpProductContract.list,
		ProductHttpProductContract.create,
		ProductHttpProductContract.update,
	]);
}

@Component({
	components: {
		'modal-or-drawer': ModalOrDrawerComponent,
		'table-import': TableImportComponent,
	},
	emits: ['close']
})
export default class ImportProductModalComponent extends Vue {
    @Prop()	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
    @Prop()	state!: boolean;
    @Prop({default:null}) type!: ProductType|null;
    @Prop({default:false}) withIngredients!: boolean;
    @Prop({required: true}) billingAccountCodes!: BillingAccountCodeApiOut[];

	csvImportProductsColumns: ImportColumn[] = ProductImportExportHelper.csvImportColumns;

    csvImportProductsWithIngredientsColumns: ImportColumn[] = [
        ...ProductImportExportHelper.csvImportColumns,
        ...[ {
			name: 'i1_name',
			translation: 'Ingrédient 1 : nom',
			matching: ['i1_name'],
			required: false
		}, {
			name: 'i1_quantity',
			translation: 'Ingrédient 1 : quantité',
			matching: ['i1_quantity'],
			required: false
		},{
			name: 'i2_name',
			translation: 'Ingrédient 2 : nom',
			matching: ['i2_name'],
			required: false
		}, {
			name: 'i2_quantity',
			translation: 'Ingrédient 2 : quantité',
			matching: ['i2_quantity'],
			required: false
		},
		{
			name: 'i3_name',
			translation: 'Ingrédient 3 : nom',
			matching: ['i3_name'],
			required: false
		}, {
			name: 'i3_quantity',
			translation: 'Ingrédient 3 : quantité',
			matching: ['i3_quantity'],
			required: false
		}]
    ];
    csvImportEntityType = ProductRevisionApiIn;

    products: ProductApiOut[]|null = null;
    billingRegions: BillingRegionApiOut[]|null = null;

    productImportExportHelper!: ProductImportExportHelper;

    loading: boolean = false;

    @AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
    @AutoWired(BillingRegionRepository) accessor billingRegionRepository!: BillingRegionRepository;

	async mounted() {
		this.loading = true;

		this.billingRegions = (await this.billingRegionRepository.callContract('list', {establishmentUid: this.establishmentUid}, {})).success();
        this.productImportExportHelper = new ProductImportExportHelper(this.establishmentUid, this.billingRegions);
        this.products = (await this.productRepository.callContract('list', {establishmentUid: this.establishmentUid},{})).success();

		this.loading = false;
	}

	close() {
		this.$emit('close');
	}
}