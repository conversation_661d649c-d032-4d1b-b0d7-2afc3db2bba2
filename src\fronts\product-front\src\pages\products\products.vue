<script lang="ts" src="./products.ts"/>

<style scoped lang="sass">
@use './products.scss' as *
</style>

<template>
    <div id="index-page" class="page">

        <forbidden-message v-if="forbidden"></forbidden-message>
        <filter-table-layout
            v-else
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :table-columns="tableColumns"
            :filters="filters"
            :applied-filters="appliedFilters"
            :filter-parameters="filterParameters"
            :drawer-opened="selectedProduct !== null"
            :pagination="pagination"
            :saved-filters="savedFilters"
            :hide-first-level-filters="true"
            @search="search($event)"
            @sorted="sorted($event)"
            @filters-changed="searchProducts($event);"
            @changed-column-preferences="saveColumnPreferences($event)"
            @next="nextPage()"
            @previous="previousPage()"
            @save-filter="saveFilter($event)"
            @select-filter="selectFilter($event)"
            @delete-filter="deleteFilter($event)"
        >
            <!--            <template v-if="products.length === 0" v-slot:top-extra-content>-->
            <!--                <div class="top-extra-content">-->
            <!--                    <div class="onboarding-cards">-->
            <!--                        <div class="card">-->
            <!--                            <i class="fa-regular fa-file-import"></i>-->

            <!--                            <div class="head">-->
            <!--                                <span class="title"> Importer depuis un autre logiciel </span>-->
            <!--                                <span class="subtitle"> Créer un produit manuellement </span>-->
            <!--                            </div>-->

            <!--                            <button class="button"> Importer </button>-->
            <!--                        </div>-->

            <!--                        <div class="card">-->
            <!--                            <i class="fa-regular fa-circle-plus"></i>-->

            <!--                            <div class="head">-->
            <!--                                <span class="title"> Créer un produit manuellement </span>-->
            <!--                                <span class="subtitle"> Créer un produit manuellement </span>-->
            <!--                            </div>-->

            <!--                            <button class="button"> Créer </button>-->
            <!--                        </div>-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--            </template>-->

            <template v-slot:table-data>
                <tr class="table-dimmer" :class="{relative: products.length === 0}" v-if="loading">
                    <td colspan="100%">
                        <div class="dimmer">
                            <div class="loader-container">
                                <div class="loader"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="table-no-data" v-else-if="products.length === 0">
                    <td colspan="100%">Aucune donnée</td>
                </tr>
                <tr v-for="product in products"
                    :class="{selected: selectedProduct && product.uid === selectedProduct.uid}">
                    <td :class="{'mobile-hidden': column.mobileHidden}"
                        v-for="column in tableColumns.filter((instance) => instance.displayed)"
                        @click="toggleSelectedProduct(product)"
                        @dblclick="toggleSelectedProduct(product,$event)"
                    >
                        <template v-if="column.name === 'uid'"> {{
                                $filters.Product_UuidTruncate(product.uid)
                            }}
                        </template>
                        <template v-else-if="column.name === 'name'">
                            <div class="product-name">
                                <product-image class="image" :establishment-uid="establishmentUid" :product="product"></product-image>
                                {{ product.lastRevision.name }}
                            </div>
                        </template>
                        <template v-else-if="column.name === 'price'">
                            {{ $filters.Money(product.lastRevision.prices[0].withTaxes) }}
                        </template>
                        <template v-else-if="column.name === 'type'">
                            {{ $filters.ProductType(product.lastRevision.type) }}
                        </template>
                        <template v-else-if="column.name === 'unit'">
                            <div v-if="product.lastRevision.unitUid">
                                {{
                                    requireUnitSystemWithUid(product.lastRevision.unitUid).units.map((unit) => unit.name).join(', ')
                                }}
                            </div>
                            <div v-else> -</div>
                        </template>
                        <template v-else-if="column.name === 'creationDatetime'">
                            {{ $filters.Date(product.creationDatetime) }}
                        </template>
                        <template v-else>

                        </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedProduct" class="empty-right-panel">
                    <img :src="$assets.selectHint"/>
                    Cliquez sur un produit pour <br/> le sélectionner
                </div>
                <div v-else class="selected-product">
                    <div class="close" @click="toggleSelectedProduct(selectedProduct)">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <div class="header">
                        <div class="left">
                            <h2> {{ selectedProduct.lastRevision.name }} </h2>
                        </div>
                        <dropdown-button
                            title="Actions"
                            icon="fa-regular fa-ellipsis-vertical"
                            button-class="black"
                            @clicked="dropdownClicked($event)"
                            :alignment="'RIGHT'"
                            :actions="[{
                                title: 'Actions',
                                actions: dropdownActions
                            }]"
                        ></dropdown-button>
                    </div>

                    <div class="product-details">
                        <product-image class="image" :establishment-uid="establishmentUid" :product="selectedProduct"></product-image>

                        <div class="product-prices">
                            <div class="data">
                                <span class="value">
                                    {{ $filters.Money(selectedProduct.lastRevision.prices[0].withoutTaxes) }}
                                </span>
                                <span class="name"> Prix HT </span>
                            </div>
                            <div class="data">
                                <span class="value">
                                    {{ $filters.Money(selectedProduct.lastRevision.prices[0].withTaxes) }}
                                </span>
                                <span class="name"> Prix TTC </span>
                            </div>
                            <div class="data">
                                <span class="value">
                                    {{
                                        getBillingRegionWithUid(selectedProduct.lastRevision.prices[0].billingCategoryRegionUid).taxes[0].percent / 1000
                                    }}%
                                </span>
                                <span class="name">
                                    {{
                                        getBillingRegionWithUid(selectedProduct.lastRevision.prices[0].billingCategoryRegionUid).taxes[0].name
                                    }}
                                 </span>
                            </div>
                        </div>

                        <div class="properties-table">
                            <div class="row">
                                <span class="title"> ID</span>
                                <span class="value"> {{ $filters.Product_UuidTruncate(selectedProduct.uid) }}</span>
                            </div>
                            <div class="row">
                                <span class="title"> Nom </span>
                                <span class="value"> {{ selectedProduct.lastRevision.name }}</span>
                            </div>
                            <div class="row">
                                <span class="title"> Date de création </span>
                                <span class="value"> {{ $filters.Date(selectedProduct.creationDatetime) }}</span>
                            </div>
                            <div class="row">
                                <span class="title"> Stock </span>
                                <span class="value"> {{
                                        selectedProduct.lastRevision.stockUid ? virtualStockHandler.estimateRemainingStock(selectedProduct.lastRevision) ?? 'Illimité' : 'Pas de stock'
                                    }} </span>
                            </div>
                            <div class="row">
                                <span class="title"> Type </span>
                                <span class="value"> {{
                                        $filters.ProductType(selectedProduct.lastRevision.type)
                                    }}</span>
                            </div>
                            <div class="row">
                                <span class="title"> Ean13 </span>
                                <span class="value"> {{ selectedProduct.lastRevision.ean13 }}</span>
                            </div>
                            <div class="row">
                                <span class="title"> Active </span>
                                <span class="value"> {{ selectedProduct.lastRevision.active }}</span>
                            </div>

                            <div class="row">
                                <span class="title"> Tangible </span>
                                <span class="value"> {{ selectedProduct.lastRevision.tangible }}</span>
                            </div>

                            <div class="row">
                                <span class="title"> Uniquable </span>
                                <span class="value"> {{ selectedProduct.lastRevision.uniquable }}</span>
                            </div>
                            <div class="row">
                                <span class="title"> Steps </span>
                                <span class="value"> {{ selectedProduct.lastRevision.groups.length }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </filter-table-layout>
    </div>

    <product-form
        v-if="showCreationModal"
        :establishment-uid="establishmentUid"
        :editing-product="editingProduct"
        :billing-regions="billingRegions"
        :unit-systems="unitSystems"
        :billing-accounts="billingAccounts"
        :billing-account-codes="billingAccountCodes"
        @created="createdProduct($event)"
        @updated="updatedProduct($event)"
        @import-products-clicked="importProductsClicked()"
        @set-billing-account="updateBillingAccounts($event)"
        @close="showCreationModal = false; editingProduct = null"
    ></product-form>

    <!--  <modal-or-drawer :state="showImportModal" @close="showImportModal = false">-->
    <!--    <table-import-->
    <!--        v-if="showImportModal"-->
    <!--        :expected-columns="csvImportColumns"-->
    <!--        :entity-type="csvImportEntityType"-->
    <!--        :entity-builder="(a: any, b: any)=>productImportExportHelper.entityBuilder(a, b,products)"-->
    <!--        :entity-saver="(a: any)=>productImportExportHelper.entitySaver(a)"-->
    <!--        @close="showImportModal = false"-->
    <!--    ></table-import>-->
    <!--  </modal-or-drawer>-->

    <import-product
        v-if="showImportModal"
        :state="showImportModal"
        :establishment-uid="establishmentUid"
        :billing-account-codes="billingAccountCodes"
        @close="showImportModal = false"
    ></import-product>

    <import-product
        v-if="showImportProductWithIngredientsModal"
        :state="showImportProductWithIngredientsModal"
        :establishment-uid="establishmentUid"
        :with-ingredients="true"
        :billing-account-codes="billingAccountCodes"
        @close="showImportProductWithIngredientsModal = false"
    ></import-product>

    <product-bulk-movement
        v-if="selectedProduct && showBulkMovementModal"
        :product="selectedProduct"
        :mode="showBulkMovementModal"
        :unit-system="selectedProduct.lastRevision.unitUid ? requireUnitSystemWithUid(selectedProduct.lastRevision.unitUid) : null"
        @created-stock="createdStock($event)"
        @close="showBulkMovementModal = null"
    ></product-bulk-movement>

    <form-modal-or-drawer
        v-if="showDeleteModal"
        :state="showDeleteModal"
        title="Supprimer le produit"
        subtitle="Êtes vous sûr de vouloir supprimer ce produit ?"
        @close="showDeleteModal = false;"
    >
        <template v-slot:content></template>

        <template v-slot:buttons>
            <button type="button" class="white button" @click="showDeleteModal = false">
                Annuler
            </button>
            <button
                type="button"
                class="red button"
                :class="{loading: deletingProduct, disabled: deletingProduct}"
                @click="deleteProduct()"
            >
                <i class="fa-regular fa-trash-alt"></i>
                Supprimer
            </button>
        </template>
    </form-modal-or-drawer>

    <form-modal-or-drawer
        :title="'Exporter les produits'"
        :subtitle="'Exportez la liste des produits filtrés'"
        :state="showProductExportModal"
        @close="showProductExportModal = false"
    >
        <template v-slot:content>
            <div class="input-group">
                <dropdown
                    :values="[{
                        image: '/img/excel.png',
                        name: 'Excel',
                        value: 'xlsx'
                    }, {
                        image: '/img/orb.png',
                        name: 'OpenOffice',
                        value: 'ods'
                    }, {
                        name: 'CSV',
                        value: 'csv'
                    }]"
                    :default-selected="selectedFileType"
                    @update="selectedFileType = $event"
                ></dropdown>
            </div>
        </template>

        <template v-slot:buttons>
            <button type="button" class="white button" @click="showProductExportModal = false"> Annuler</button>
            <button type="button" class="button"
                    @click="productImportExportHelper.exportProducts(filters, selectedFileType)">
                <i class="fa-regular fa-arrow-down-to-line"></i>
                Exporter
            </button>
        </template>

    </form-modal-or-drawer>

    <product-fast-billing-account-binding v-if="showBillingAccountBatchModal" @close="showBillingAccountBatchModal = false"></product-fast-billing-account-binding>

    <quick-product-into-pos
        v-if="showQuickPosCategoryModal"
        :product-to-add="showQuickPosCategoryModal"
        @close="showQuickPosCategoryModal = null"
    ></quick-product-into-pos>

    <toast-manager></toast-manager>
</template>