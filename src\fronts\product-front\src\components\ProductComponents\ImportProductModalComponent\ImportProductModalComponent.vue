<script lang="ts" src="./ImportProductModalComponent.ts">
</script>

<style lang="sass" scoped>
@use './ImportProductModalComponent.scss' as *
</style>

<template>
    <div v-if="loading">Chargement...</div>
    <template v-else-if="products">
        <modal-or-drawer :state="state" @close="close()">
            <table-import
                v-if="state"
                :expected-columns="withIngredients?csvImportProductsWithIngredientsColumns:csvImportProductsColumns"
                :entity-type="csvImportEntityType"
                :entity-builder="(a: any, b: any) => productImportExportHelper.entityBuilder(a, b,products ?? [], type)"
                :entity-saver="(a: any) => productImportExportHelper.entitySaver(a, billingAccountCodes)"
                @close="close()"
            ></table-import>
        </modal-or-drawer>
    </template>
    <template v-else>
        ImportProductWithIngredientsComponent : Component non intialisé
    </template>
</template>