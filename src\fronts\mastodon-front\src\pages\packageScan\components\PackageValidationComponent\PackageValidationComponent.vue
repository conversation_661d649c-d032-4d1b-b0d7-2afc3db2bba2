<script lang="ts" src="./PackageValidationComponent.ts"/>

<style lang="sass" scoped>
@import './PackageValidationComponent.scss'
</style>

<template>
    <div class="package-validation-component">
        <div class="container">
            <div class="actions">
                <div class="action-group" @click="close()">
                    <div class="action">
                        <i class="fa-regular fa-arrow-left"></i>
                    </div>
                    <span class="text"> retour </span>
                </div>
            </div>

            <div class="done" v-if="notScannedObjects.length === 0">
                Le colis est complet

                <button class="button" :class="{loading: saving, disabled: saving}" @click="validatePackage()">
                    Valider le colis
                </button>
            </div>

            <template v-else>
                <h3> Objets non vérifiés ({{ notScannedObjects.length }})</h3>

                <div class="objects">
                    <div class="object" v-for="objectData of notScannedObjects" @click="showActionsForObject = objectData">
                        <div class="top">
                            <span class="name"> {{ getObjectTranslation(objectData.code) }} </span>

                            <div class="images">
                                <i class="fa-regular fa-image" v-if="objectData.images"></i>
                                <i class="fa-regular fa-image" v-if="objectData.returnImages"></i>
                            </div>

                            <i class="fa-regular fa-ellipsis-vertical"></i>
                        </div>
                        <div class="bottom" v-if="objectData.comment">
                            {{ objectData.comment }}
                        </div>
                    </div>
                </div>
            </template>

            <h3> Objets vérifiés ({{ scannedObjects.length }})</h3>

            <div class="objects">
                <div class="object" v-for="objectData of scannedObjects" @click="showActionsForObject = objectData">
                    <div class="top">
                        <span class="name"> {{ getObjectTranslation(objectData.code) }} </span>

                        <div class="images">
                            <i class="fa-regular fa-image" v-if="objectData.images"></i>
                            <i class="fa-regular fa-image" v-if="objectData.returnImages"></i>
                        </div>

                        <i class="fa-regular fa-ellipsis-vertical"></i>
                    </div>
                    <div class="bottom" v-if="objectData.comment">
                        {{ objectData.comment }}
                    </div>
                </div>
            </div>
        </div>

        <div class="scan-state success" v-if="scanState === 'SUCCESS'"></div>
        <div class="scan-state error" v-else-if="scanState === 'ERROR'"></div>

        <div class="scanning-modal" v-if="scanning">
            <div class="container">
                <div class="loading-container">
                    <div class="loader"></div>
                </div>
            </div>
        </div>

        <item-actions
            v-if="showActionsForObject"
            :actions="getObjectActions(showActionsForObject)"
            @action-clicked="actionClicked($event)"
            @close="showActionsForObject = null"
        ></item-actions>

        <object-form
            v-if="showObjectForm"
            :editing-object="showObjectForm"
            @updated="updateObject($event)"
            @close="showObjectForm = null; setupListener()"
        ></object-form>

        <camera
            v-if="showCamera && showCamera.opened"
            @photo-captured="photoCaptured($event)"
            @close="showCamera.opened = false"
        ></camera>

        <image-gallery
            v-if="showObjectImage || showObjectReturnImage"
            :images="showObjectImage ? showObjectImage.images : (showObjectReturnImage ? showObjectReturnImage.returnImages : [])"
            @close="showObjectImage = null; showObjectReturnImage = null"
        ></image-gallery>
    </div>
</template>